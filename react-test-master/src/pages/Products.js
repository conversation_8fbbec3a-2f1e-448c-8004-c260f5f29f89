// Import libraries
import React, { useState, useEffect } from "react";
// Import components
import Col from "react-bootstrap/Col";
import Row from "react-bootstrap/Row";
import Image from "react-bootstrap/Image";
import Button from "react-bootstrap/Button";
import ListGroup from "react-bootstrap/ListGroup";
// Export
export default function Products() {
  // State
  const [products, setProducts] = useState();

  // Fetch products on component mount
  useEffect(() => {
    fetch("/data/products.json")
      .then((response) => {
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
      })
      .then((data) => setProducts(data))
      .catch((error) => {
        console.error("Error fetching JSON:", error);
      });
  }, []);

  // Render
  return (
    <ListGroup as="ol" className="w-75 mx-auto">
      {!products
        ? "Loading products..."
        : products.map((product) => {
            return (
              <ListGroup.Item key={product.id} as="li" className="p-3">
                <Row>
                  <Col md={2}>
                    <Image
                      src={product.image}
                      alt="Generic placeholder"
                      fluid
                      rounded
                    />
                  </Col>
                  <Col
                    md={10}
                    className="d-flex flex-column justify-content-center"
                  >
                    <h5 className="mt-0 font-weight-bold mb-2">
                      {product.name}
                    </h5>
                    <p className="font-italic text-muted mb-0 small mb-3">
                      {product.short_description}
                    </p>
                    <div className="d-flex align-items-center justify-content-between mt-1">
                      <h6 className="font-weight-bold my-2">
                        ${product.price}
                      </h6>
                      <Button
                        variant="outline-primary"
                        size="sm"
                        style={{
                          fontSize: "0.7rem",
                        }}
                      >
                        Add to Cart
                      </Button>
                    </div>
                  </Col>
                </Row>
              </ListGroup.Item>
            );
          })}
    </ListGroup>
  );
}
